const handleExportAngularCode = async () => {
    console.log("Handle Click for API response in Angular");

    // Generate a new cancel token source for this specific request
    source = axios.CancelToken.source();

    const apiPayload = {
      mode: "MLO_HTML_TO_ANGULAR",
      technology: "Angular",
      code: apiResponse,
      userSignature: accounts[0].username,
    };
    setModalContent({ title: "Exporting Code", desc: "Please Wait..." });
    setLoading(true);
    setError(null);

    try {
      const response = await axiosInstance.post("/image_to_code/code_convert", apiPayload, {
        cancelToken: source.token
      });

      if (response.data.status_code === 200) {
        const convertedCode = response.data.converted_code;
        console.log("newCode_Angular", convertedCode);
        setExportToAngular(convertedCode);

        // Handle Download/Export Angular code with best practices
        await createAndDownloadZip(convertedCode);
      } else {
        console.log("There seems to be some error");
        console.error("Error response data:", response.data);
        setError("Failed to get a valid response from the server.");
      }
    } catch (error) {
      if (axios.isCancel(error)) {
        console.warn("Request canceled:", error.message);
      } else if (axios.isAxiosError(error)) {
        console.error("Error in API call:", error.message);
        if (error.response) {
          console.error("Error status:", error.response.status);
          console.error("Error data:", error.response.data);
          setError(
            `Server error: ${error.response.data.message || error.message}`
          );
        } else {
          setError(`Network error: ${error.message}`);
        }
      } else {
        console.error("Unexpected error:", error);
        setError("An unexpected error occurred.");
      }
    } finally {
      setLoading(false);
    }
  };

  /**
   * Creates and downloads a zip file from the converted code object
   * @param {Object} convertedCode - Object with filename as key and file content as value
   */
  const createAndDownloadZip = async (convertedCode) => {
    try {
      const zip = new JSZip();
      
      // Validate that convertedCode is an object
      if (!convertedCode || typeof convertedCode !== 'object') {
        throw new Error('Invalid converted code format');
      }

      // Add each file to the zip
      Object.entries(convertedCode).forEach(([filename, content]) => {
        // Validate filename and content
        if (!filename || typeof filename !== 'string') {
          console.warn('Skipping invalid filename:', filename);
          return;
        }

        if (content === null || content === undefined) {
          console.warn(`Skipping file with null/undefined content: ${filename}`);
          return;
        }

        // Convert content to string if it's not already
        const fileContent = typeof content === 'string' ? content : String(content);
        
        // Add file to zip with proper path handling
        const sanitizedFilename = sanitizeFilename(filename);
        zip.file(sanitizedFilename, fileContent);
        
        console.log(`Added file to zip: ${sanitizedFilename} (${fileContent.length} characters)`);
      });

      // Check if any files were added
      const fileCount = Object.keys(zip.files).length;
      if (fileCount === 0) {
        throw new Error('No valid files found to zip');
      }

      console.log(`Creating zip with ${fileCount} files`);

      // Generate zip file with compression
      const zipBlob = await zip.generateAsync({ 
        type: "blob",
        compression: "DEFLATE",
        compressionOptions: {
          level: 6
        }
      });

      // Generate filename with timestamp for uniqueness
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
      const zipFilename = `angular-component-${timestamp}.zip`;

      // Download the zip file
      saveAs(zipBlob, zipFilename);
      
      console.log(`Successfully created and downloaded: ${zipFilename}`);
    } catch (error) {
      console.error("Error creating zip file:", error);
      setError(`Failed to create zip file: ${error.message}`);
      throw error;
    }
  };

  /**
   * Sanitizes filename to ensure it's safe for zip file creation
   * @param {string} filename - Original filename
   * @returns {string} - Sanitized filename
   */
  const sanitizeFilename = (filename) => {
    // Remove any path traversal attempts and invalid characters
    return filename
      .replace(/[<>:"|?*]/g, '') // Remove invalid characters
      .replace(/\.\./g, '') // Remove path traversal
      .replace(/^\/+|\/+$/g, '') // Remove leading/trailing slashes
      .trim();
  };
